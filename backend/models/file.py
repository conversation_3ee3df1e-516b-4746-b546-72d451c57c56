import os
import time
from datetime import datetime

from elasticsearch import Elasticsearch
from elasticsearch_dsl import Byte, Date
from elasticsearch_dsl import Document as DSLDocument
from elasticsearch_dsl import InnerDoc, Integer, Keyword, Nested, Object, Text
from elasticsearch_dsl.connections import connections
from yunfu.common import ConfigUtils, yfid

from backend.utils import MinioUtils

conf = ConfigUtils.load('conf/config.yaml')
connections.configure(default={'hosts': conf['ES_CONFIGS']['host']})


class Entity(InnerDoc):
    text = Keyword(index=False)
    type = Keyword(index=False)
    offset = Keyword(index=False)
    length = Keyword(index=False)
    linked_uuid = Keyword()
    linked_name = Keyword(index=False)


class Shared(InnerDoc):
    """实体"""
    id = Keyword()
    permission = Keyword()


class NlpResult(InnerDoc):
    keyword_json = Text(index=False)
    event_json = Text(index=False)
    attribute_json = Text(index=False)


class UserFile(DSLDocument):
    delete_batch_num = Keyword()
    delete_flag = Keyword()  # 判断删除，1是删除，0是
    suffix = Keyword()
    file_id = Keyword()
    file_name = Text(analyzer='ik_smart', search_analyzer='ik_searcher')
    file_path = Keyword()
    file_size = Integer()
    is_dir = Keyword()  # 判断是目录还是文件，1是目录，0是文件
    created_at = Date()
    deleted_at = Date()
    updated_at = Date()
    user_id = Keyword()

    published_at = Date()
    source = Keyword()
    title = Text(analyzer='ik_smart', search_analyzer='ik_searcher')
    content = Text(analyzer='ik_smart', search_analyzer='ik_searcher')
    title_zh = Text(analyzer='ik_smart', search_analyzer='ik_searcher')
    content_zh = Text(analyzer='ik_smart', search_analyzer='ik_searcher')
    country = Keyword()
    language = Keyword()
    tags = Keyword(multi=True)
    domain = Keyword()
    sentiment = Byte()
    related_countries = Keyword(multi=True)
    entities = Object(Entity)
    entities_raw = Object(Entity)
    nlp_results = Object(NlpResult)
    url = Keyword()
    shared_teams = Nested(Shared)
    shared_users = Nested(Shared)
    parse_status = Keyword()
    parse_step = Keyword()
    org_id = Keyword()
    task_id = Keyword()             # 爬虫任务ID

    class Index:
        name = 'data_doc_user_file'
        settings = conf['ANALYSIS_CONF']

    @classmethod
    def upload_file(
        cls, user_id, org_id, file_path, file_, identifier, total_size=0, doc_meta=None,
        tags=None, resource_url='', spider_meta=None, flow_args=None,
        suffix='', task_id=None, file_name=None,
    ) -> str:
        es_conf = conf['ES_CONFIGS']
        client = Elasticsearch(es_conf['host'], timeout=180)

        ts = round(time.time() * 1000)
        doc_meta = doc_meta or {}
        if not resource_url and file_:
            resource_url = f'{file_.name}{ts}'
        if not identifier:
            identifier = yfid(resource_url)
        if total_size == 0:
            file_.seek(0, os.SEEK_END)
            total_size = file_.tell()
            file_.seek(0, 0)
        file_id = identifier
        file_path = file_path.rstrip('/')
        file_url = f'/{user_id}{file_path}/{identifier}'
        if file_:
            MinioUtils.write('yfkm', file_url, file_, total_size)
        file_data = File2(_id=file_id,
                          create_user_id=user_id,
                          modify_user_id=user_id,
                          file_size=total_size,
                          file_url=file_url,
                          identifier=identifier,
                          tags=tags,
                          task_id=task_id,
                          resource_url=resource_url,
                          spider_meta=spider_meta,
                          flow_args=flow_args,
                          created_at=datetime.now(),
                          updated_at=datetime.now())
        file_data.save(using=client, index=es_conf['data_doc_file_index'], refresh=True)
        user_file = UserFile(
            user_id=user_id,
            org_id=org_id,
            file_name='.'.join(file_name.split('.')[:-1]),
            file_path=f'{user_id}{file_path}' if file_path.startswith('/') else f'{user_id}/{file_path}',
            is_dir=0,
            delete_flag=0,
            file_id=file_id,
            file_size=total_size,
            tags=tags,
            parse_status=0,
            created_at=datetime.now(),
            updated_at=datetime.now(),
            suffix=suffix,
            task_id=task_id,
            **doc_meta,
        )
        user_file.save(using=client, index=es_conf['data_doc_user_file_index'], refresh=True)
        return user_file.meta['id']     # type: ignore


class File2(DSLDocument):
    """上传文件"""
    create_user_id = Keyword()  # 上传用户id
    file_size = Keyword()  # 文件大小
    file_status = Keyword()  # 文件状态默认为1
    file_url = Keyword()
    identifier = Keyword()
    created_at = Date()
    updated_at = Date()
    storage_type = Keyword()
    modify_user_id = Keyword()

    tags = Keyword(multi=True)              # 数据标签
    task_id = Keyword()                     # 爬虫任务ID TODO(deprecated, use UserFile.task_id instead)
    resource_url = Keyword()                # 资源链接
    spider_meta = Object(enabled=False)     # 爬虫元数据
    flow_args = Object(enabled=False)       # 爬虫流程参数

    class Index:
        name = 'data_doc_file'
        settings = conf['ANALYSIS_CONF']
