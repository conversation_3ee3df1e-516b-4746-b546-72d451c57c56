import os
from typing import Any, Callable, Dict, List

import django
from django.conf import settings
from django.db import models
from django.db.models import Max
from elasticsearch_dsl import Date, DenseVector, Integer, Keyword, Text
from yunfu.common import ConfigUtils, yfid
from yunfu.yfint.schema import Entity

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "backend.settings")
django.setup()
config = ConfigUtils.load('conf/config.yaml')

from django.db import close_old_connections  # noqa


def close_old_database_connections(func: Callable):  # noqa: ANN201, ANN001

    def wrapper(*args: List, **kwargs: Dict):  # noqa: ANN201, ANN001
        close_old_connections()
        return func(*args, **kwargs)

    return wrapper


class YfModel(models.Model):
    """基础model类

    """
    created_at = models.DateTimeField(verbose_name='创建时间', auto_now_add=True)
    updated_at = models.DateTimeField(verbose_name='修改时间', auto_now=True)

    class Meta:
        abstract = True
        ordering = ['id']


class Team(YfModel):
    """用户组

    """
    name = models.CharField(max_length=255, verbose_name='组名')
    conf = models.JSONField(verbose_name='组配置', default=dict)
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, default=None)
    description = models.CharField(max_length=255, verbose_name='描述', blank=True, null=True, default='')
    is_activate = models.BooleanField(verbose_name='启用状态', default=True)


class GraphDbs(models.TextChoices):
    NEO4J = "neo4j"
    NEBULA = "nebula"


class Kg(YfModel):

    class KgRanges(models.IntegerChoices):
        PRIVATE = 1  # 私有
        GROUP_OPENNESS = 2  # 组内公开
        TOTAL_OPENNESS = 3  # 完全公开

    class TypeChoices(models.TextChoices):
        ONTOLOGY = 'ontology'  # 模版本体
        KG = 'kg'  # 图谱
        UPDATE = 'update'  # 更新用图谱

    name = models.CharField('名称', max_length=255)
    description = models.TextField(verbose_name='简介', null=True, blank=True)
    ontology_num = models.IntegerField(verbose_name='本体计数', default=0)
    property_num = models.IntegerField(verbose_name='属性计数', default=0)
    relation_num = models.IntegerField(verbose_name='关系计数', default=0)
    status = models.SmallIntegerField(verbose_name='状态', default=0)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, verbose_name='操作用户')
    type = models.CharField('图谱类型', choices=TypeChoices.choices, default=TypeChoices.KG, max_length=50)
    team = models.ForeignKey(Team, verbose_name='用户组', on_delete=models.CASCADE, blank=True, null=True)
    range = models.IntegerField(verbose_name='可见范围', choices=KgRanges.choices, blank=True, default=1)
    parent = models.ForeignKey(
        'self',
        verbose_name='裁剪来源',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        db_column='pid',
        related_name='children')
    center_node_eid = models.CharField(verbose_name='中心节点eid', max_length=255, null=True, blank=True, default=None)
    db = models.CharField(
        verbose_name="图数据库", max_length=20, default=GraphDbs.NEO4J
    )

    @property
    def db_config(self) -> dict:
        return config["NEO4J_CONFIGS"]["db"] if self.db == GraphDbs.NEO4J else config["nebula"]  # type: ignore

    @property
    def space(self) -> str:
        return f"KG{self.id}"

    class Meta:
        db_table = 'yunfu_due_kg_kg'
        verbose_name = 'kg'
        ordering = ['-updated_at']


class BaseCount(YfModel):
    """计数表"""

    kg = models.ForeignKey(Kg, on_delete=models.CASCADE, verbose_name='对应的图谱')
    name = models.CharField("名字", max_length=500)
    count = models.IntegerField(verbose_name='计数', default=0)

    class Meta:
        db_table = 'yunfu_due_kg_basecount'
        ordering = ['-count']
        verbose_name = '计数表'


class KgVersion(YfModel):
    """图谱版本表"""

    class SyncStatusChoices(models.IntegerChoices):
        Synchronized = 1  # 已同步
        Synchronizing = 2  # 同步中

    kg = models.ForeignKey(Kg, related_name='versions', on_delete=models.CASCADE, verbose_name='对应的图谱')
    name = models.CharField(verbose_name='版本名称', max_length=255)
    number = models.IntegerField(verbose_name='版本号', default=0)
    description = models.TextField(verbose_name='简介', null=True, blank=True)
    entities = models.ManyToManyField(BaseCount, related_name='kg_count_entities', blank=True)
    relations = models.ManyToManyField(BaseCount, related_name='kg_count_relations', blank=True)
    properties = models.ManyToManyField(BaseCount, related_name='kg_count_properties', blank=True)
    entity_count = models.IntegerField(verbose_name='实体数量', default=0)
    property_count = models.IntegerField(verbose_name='属性数量', default=0)
    relation_count = models.IntegerField(verbose_name='关系数量', default=0)
    full_file = models.CharField(verbose_name='多模态图谱压缩包', max_length=100, default='', blank=True, null=True)
    center_node_eid = models.CharField(verbose_name='中心节点eid', max_length=255, null=True, blank=True, default=None)
    hot_entities = models.JSONField(verbose_name='热门实体', null=True, blank=True, default=[])
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, verbose_name='创建用户')
    ontology_file = models.CharField(verbose_name='本体三元组文件id', max_length=100, default='', blank=True)
    entity_file = models.CharField(verbose_name='实体三元组文件id', max_length=100, default='', blank=True)
    kg_file = models.CharField(verbose_name='全量三元组文件id', max_length=100, default='', blank=True)
    parent = models.CharField(verbose_name='数据来源', max_length=20, default='', blank=True)
    sync_status = models.IntegerField(verbose_name='本体操作同步状态',
                                      choices=SyncStatusChoices.choices,
                                      default=SyncStatusChoices.Synchronized)
    ontology_tree = models.JSONField(verbose_name='类别体系', null=True, blank=True, default=[])

    class Meta:
        db_table = 'yunfu_due_kg_kgversion'
        verbose_name = 'kg_version'
        ordering = ['-created_at']

    @classmethod
    def number2str(cls, number) -> str:
        if number == 0:
            return 'c'
        return f'v{number}'

    @classmethod
    def get_next_number(cls, kg_id) -> int:
        number = cls.objects.filter(kg__id=kg_id).aggregate(Max('number'))['number__max']
        return (number or 0) + 1

    @classmethod
    def node_has_history(cls, labels, kg_id) -> bool:
        next_kg_version_number = cls.get_next_number(kg_id)
        if next_kg_version_number == 1:
            return False
        for i in range(1, next_kg_version_number):
            if cls.number2str(i) in labels:
                return True
        return False

    @classmethod
    def relation_has_history(cls, relations_properties, kg_id) -> bool:
        next_kg_version_number = cls.get_next_number(kg_id)
        if next_kg_version_number == 1:
            return False
        for i in range(1, next_kg_version_number):
            if relations_properties.get(cls.number2str(i)):
                return True
        return False


class OntologyEditLog(YfModel):
    """本体编辑记录表"""

    class OperationStatusChoices(models.IntegerChoices):
        UnSynchronized = 1  # 未同步
        Synchronized = 2  # 已同步

    user_id = models.IntegerField(verbose_name='用户id', null=True, blank=True)
    kg_id = models.IntegerField(verbose_name='图谱id', null=True, blank=True)
    operation = models.IntegerField(verbose_name='操作类型', null=True, blank=True)
    src = models.CharField(verbose_name='操作前数据', max_length=255)
    dst = models.CharField(verbose_name='操作后数据', max_length=255)
    status = models.IntegerField(verbose_name='操作状态',
                                 choices=OperationStatusChoices.choices,
                                 default=OperationStatusChoices.UnSynchronized)

    class Meta:
        db_table = 'yunfu_due_kg_ontology_edit_log'
        verbose_name = 'ontology_edit_log'
        ordering = ['updated_at']


class ResourceChangeLog(YfModel):
    resource_name = models.CharField(verbose_name='资源名', max_length=512)
    resource_id = models.CharField(verbose_name='资源id', max_length=100)
    resource_type = models.CharField(verbose_name='资源类别', max_length=100)
    yfid = models.CharField(verbose_name='唯一标识', max_length=30, default='', blank=True)
    operation = models.CharField(verbose_name='操作', max_length=100)
    value = models.CharField(verbose_name='操作值', default='', max_length=100)
    sources = models.JSONField(verbose_name='操作来源资源', max_length=100, null=True, blank=True)
    detail = models.JSONField(verbose_name='操作信息详情', null=True, blank=True, default=list)

    class Meta:
        db_table = 'yunfu_due_data_resourcechangelog'
        ordering = ['-updated_at']
        verbose_name = '数据溯源记录表'
        indexes = [
            models.Index(fields=['resource_id']),
            models.Index(fields=['resource_id', 'value']),
            models.Index(fields=['yfid']),
        ]

    @classmethod
    def create_yfid(cls, resource_name, resource_id, operation, value) -> Any:
        return yfid(f"{resource_name}__{resource_id}__{operation}__{value}")

    @classmethod
    def create_logs(cls, logs) -> None:
        for log in logs:
            resource_name = log['resource_name']
            resource_id = log['resource_id']
            resource_type = log['resource_type']
            operation = log['operation']
            value = log['value']
            source_id = log['source_id']
            yfid_ = ResourceChangeLog.create_yfid(resource_name, resource_id, operation, value)
            if ResourceChangeLog.objects.filter(yfid=yfid_).exists():
                return
            new_log = ResourceChangeLog()
            new_log.resource_name = resource_name
            new_log.resource_id = resource_id
            new_log.resource_type = resource_type
            new_log.operation = operation
            new_log.value = value
            new_log.yfid = yfid_
            new_log.sources = [source_id]
            if log.get('detail'):
                new_log.detail = log['detail']
            new_log.save()


class Linker(Entity):
    """链接表"""
    show_name = Text(analyzer='ik_max_word', search_analyzer='ik_searcher')
    alias = Text(analyzer='ik_max_word', search_analyzer='ik_searcher')
    linked_name = Text(analyzer='ik_max_word', search_analyzer='ik_searcher')
    linking_names = Keyword()
    linked_eid = Keyword()
    embedding = DenseVector(dims=768)
    content_type = Keyword()
    version = Keyword()
    created_at = Date()
    node_type = Keyword()
    kg_id = Integer()

    class Index:
        settings = config['ANALYSIS_CONF']
