from pydantic import BaseModel


class UpdateKbqaParams(BaseModel):
    kg_id: int
    version: str = "c"


class SyncOntologyEditParams(BaseModel):
    kg_id: int


class CreateParams(BaseModel):
    documents: list


class ReindexParams(BaseModel):
    kg_id: int


class UpdateOntologyTreeParams(BaseModel):
    kg_id: int
    version_number: int = 0


class ExportKgParams(BaseModel):
    kg_id: int
    version_number: int
    org_id: str
