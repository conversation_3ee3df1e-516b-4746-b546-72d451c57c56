from typing import Dict

from asgiref.sync import sync_to_async
from docarray import DocumentArray
from yfflow import YfExecutor, requests

from backend.models.schemas import ExportKgParams
from backend.modules.export.pipelines import ExportKgPipeline


class ExportKgExecutor(YfExecutor):

    @requests(on='/export_kg')
    async def create(self, docs: DocumentArray, **kwargs: Dict[str, dict]) -> DocumentArray:
        for doc in docs:
            params = ExportKgParams(**doc.tags)
            self.logger.info(f'[export kg] params: {params}')
            await sync_to_async(ExportKgPipeline.run)(params)
