from typing import Dict

from asgiref.sync import sync_to_async
from docarray import DocumentArray
from yfflow import YfExecutor, requests

from backend.models.schemas import UpdateOntologyTreeParams
from backend.modules.update_ontology_tree.pipelines import UpdateOntologyTreePipeline


class UpdateOntologyTreeExecutor(YfExecutor):

    @requests(on='/ontology_tree')
    async def create(self, docs: DocumentArray, **kwargs: Dict[str, dict]) -> DocumentArray:
        for doc in docs:
            params = UpdateOntologyTreeParams(**doc.tags)
            self.logger.info(f'[update ontology tree] params: {params}')
            await sync_to_async(UpdateOntologyTreePipeline.run)(params)
