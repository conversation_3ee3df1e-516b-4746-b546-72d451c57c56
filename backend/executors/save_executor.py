from typing import Dict

from asgiref.sync import sync_to_async
from docarray import DocumentArray
from yfflow import YfExecutor, requests

from backend.models.schemas import CreateParams, ReindexParams
from backend.modules.save.pipelines import CreatePipeline, ReindexPipeline


class SaveExecutor(YfExecutor):

    @requests(on="/create")
    async def create(
        self, docs: DocumentArray, **kwargs: Dict[str, dict]
    ) -> DocumentArray:
        for doc in docs:
            params = CreateParams(**doc.tags)
            self.logger.info(f'[save create] params: {params}')
            await sync_to_async(CreatePipeline.run)(params)

    @requests(on="/reindex")
    async def reindex(
        self, docs: DocumentArray, **kwargs: Dict[str, dict]
    ) -> DocumentArray:
        for doc in docs:
            params = ReindexParams(**doc.tags)
            self.logger.info(f'[save reindex] params: {params}')
            await sync_to_async(ReindexPipeline.run)(params)
