from typing import Dict

from asgiref.sync import sync_to_async
from docarray import DocumentArray
from yfflow import YfExecutor, requests

from backend.models.schemas import UpdateKbqaParams
from backend.modules.update_kbqa.pipelines import UpdateKbqaPipeline


class UpdateKbqaExecutor(YfExecutor):

    @requests(on="/update_kbqa")
    async def create(
        self, docs: DocumentArray, **kwargs: Dict[str, dict]
    ) -> DocumentArray:
        for doc in docs:
            params = UpdateKbqaParams(**doc.tags)
            await sync_to_async(UpdateKbqaPipeline.run)(params)
