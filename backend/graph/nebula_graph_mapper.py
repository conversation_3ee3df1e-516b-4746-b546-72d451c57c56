from typing import List, Optional, Union, overload

from yunfu.common import LogUtils
from yunfu.db.graph.core.models.config import Config
from yunfu.db.graph.graph.graph_dbs import NebulaGraphDb
from yunfu.db.graph.models import Edge, EdgeType, Node, NodeProp, NodeType

from backend.utils import TimeUtils

from .base_graph_mapper import BaseGraphMapper

logger = LogUtils.get_logger(__name__)


class NebulaGraphMapper(BaseGraphMapper):

    graph_db: NebulaGraphDb
    # Nebula 暂时不支持下划线前缀，所以记录下带下划线的属性
    node_private_props = ["show_name", "type", "eid", "create_time", "update_time"]
    edge_private_props = ["rid", "create_time", "update_time"]

    def __init__(self, db_config: dict, enable_version: bool = True):
        self.graph_db = NebulaGraphDb(
            Config(db=db_config, version={"enabled": enable_version})
        )

    def get_nodes(self, space: str, skip: int, limit: int, types: List[str] = [], props: list = []) -> List[Node]:
        nodes = super().get_nodes(space, skip, limit, types, props)
        return [self._restore_node_props(node) for node in nodes]

    def get_node_by_name(self, space: str, version: str, types: List[str], name: str) -> Node:
        node = super().get_node_by_name(space, version, types, name)
        if node is None:
            return None
        return self._restore_node_props(node)

    def _pre_process_node(self, node: Node) -> None:
        """预处理节点"""
        now_time = TimeUtils.now_str()
        node.props["_create_time"] = now_time
        node.props["_update_time"] = now_time
        props = {}
        for key, value in node.props.items():
            props[key.strip("_")] = value
        node.props = props

    def insert_node(self, space: str, node: Node) -> Node:
        """插入点"""
        graph = self.graph_db.get_graph(space)
        self._pre_process_node(node)
        node_types = graph.node_types
        for name in node.types:
            if name not in node_types:
                graph.create_node_type(NodeType(name=name))
        return self._restore_node_props(graph.insert_node(node))

    def _pre_process_edge(self, edge: Edge) -> None:
        """预处理边"""
        now_time = TimeUtils.now_str()
        props = {"name": edge.type, "create_time": now_time, "update_time": now_time}
        for key, value in edge.props.items():
            props[key.strip("_")] = value
        edge.props = props

    def _format_edge_type(self, name: str) -> EdgeType:
        """创建边类型"""
        return EdgeType(
            name=name,
            props=[
                {"name": "name", "type": "string"},
                {"name": "rid", "type": "string"},
                {"name": "create_time", "type": "string"},
                {"name": "update_time", "type": "string"},
                {"name": "c", "type": "bool"},
                {"name": "id", "type": "string"},
            ],
        )

    def insert_edge(self, space: str, edge: Edge) -> Edge:
        """插入边"""
        graph = self.graph_db.get_graph(space)
        self._pre_process_edge(edge)
        edge_types = graph.edge_types
        if edge.type not in edge_types:
            edge_type = self._format_edge_type(edge.type)
            graph.create_edge_type(edge_type)
        edge = graph.insert_edge(edge)
        return self._restore_edge_props(edge)

    def get_all_relations_by_eid(self, space: str, eid: str) -> List[Edge]:
        graph = self.graph_db.get_graph(space)
        edges = graph.edges.match(
            src_node_props=[("eid", "=", eid)]
        ).all()
        return [self._restore_edge_props(edge) for edge in edges]

    @overload
    def _restore_props(self, node_or_edge: Node, private_props: List[str]) -> Node: ...

    @overload
    def _restore_props(self, node_or_edge: Edge, private_props: List[str]) -> Edge: ...

    def _restore_props(
        self, node_or_edge: Union[Node, Edge], private_props: List[str]
    ) -> Union[Node, Edge]:
        props = {}
        for key, value in node_or_edge.props.items():
            if key in private_props:
                props[f"_{key}"] = value
            else:
                props[key] = value
        node_or_edge.props = props
        return node_or_edge

    def _restore_node_props(self, node: Node) -> Node:
        return self._restore_props(node, self.node_private_props)

    def _restore_edge_props(self, edge: Edge) -> Edge:
        edge.src_node = self._restore_node_props(edge.src_node)
        edge.dst_node = self._restore_node_props(edge.dst_node)
        return self._restore_props(edge, self.edge_private_props)

    def export_entities(
        self,
        space: str,
        skip: int = 0,
        limit: int = 20,
        version: str = "c",
        export_type: str = "kg",
    ) -> list:
        """导出实体"""
        graph = self.graph_db.get_graph(space)
        if export_type == "ontology":
            query = f"MATCH (n:{space}:{version}:concept) RETURN n SKIP {skip} LIMIT {limit}"
        elif export_type == "entity":
            query = (
                f"MATCH (n:{space}:{version}) WITH n, labels(n) AS labels WHERE NOT 'concept' IN labels "
                f"RETURN n SKIP {skip} LIMIT {limit}"
            )
        else:
            query = f"MATCH (n:{space}:{version}) RETURN n SKIP {skip} LIMIT {limit}"
        nodes = graph.data_wrapper.as_nodes(graph.client.run(query).data, output="n")
        return [self._restore_node_props(node) for node in nodes]

    def export_relations(
        self,
        space: str,
        skip: int = 0,
        limit: int = 20,
        version: str = "c",
        export_type: str = "kg",
    ) -> list:
        """导出关系"""
        graph = self.graph_db.get_graph(space)
        if export_type == "ontology":
            query = (
                f"MATCH (n:{space}:{version}:concept)-[r]->(m:{space}:{version}:concept)"
                f" WHERE r.{version} == true RETURN r, n, m SKIP {skip} LIMIT {limit}"
            )
        elif export_type == "entity":
            query = (
                f"MATCH (n:{space}:{version})-[r]->(m:{space}:{version})"
                f" WITH r, n, m, labels(n) AS src_labels, labels(m) AS dst_labels "
                f" WHERE r.{version} == true AND NOT 'concept' IN src_labels AND NOT 'concept' IN dst_labels "
                f"RETURN r, n, m SKIP {skip} LIMIT {limit}"
            )
        else:
            query = (
                f"MATCH (n:{space}:{version})-[r]->(m:{space}:{version})"
                f" WHERE r.{version} == true RETURN r, n, m SKIP {skip} LIMIT {limit}"
            )
        edges = graph.data_wrapper.as_edges(
            graph.client.run(query).data,
            output="r", src_output="n", dst_output="m")
        return [self._restore_edge_props(edge) for edge in edges]

    def get_all_onto_relations_by_labels(self, space, version, types, name):
        edges = super().get_all_onto_relations_by_labels(space, version, types, name)
        return [self._restore_edge_props(edge) for edge in edges]

    def delete_node_version_by_eid(self, space: str, eid: str, version: str):
        """删除节点版本"""
        graph = self.graph_db.get_graph(space)
        query = f"""DELETE TAG {version} FROM '{eid}'"""
        graph.client.run(query)

    def delete_relation_version_by_id(self, space: str, id: int, version: str) -> None:
        # TODO: 版本
        graph = self.graph_db.get_graph(space)
        graph.delete_edge(id)

    def get_edge_by_node_name(
            self, space: str, src_node_name: str, dest_node_name: str, relation_name: str) -> Optional[Edge]:
        edge = super().get_edge_by_node_name(space, src_node_name, dest_node_name, relation_name)
        if edge is None:
            return None
        return self._restore_edge_props(edge)

    def get_all_relations_by_node_with_labels(self, space, src_node_types, src_id, dst_node_types=[]):
        edges = super().get_all_relations_by_node_with_labels(space, src_node_types, src_id, dst_node_types)
        return [self._restore_edge_props(edge) for edge in edges]

    def partial_update_node(self, space: str, node_id: str, props: dict) -> Node:
        graph = self.graph_db.get_graph(space)
        now_time = TimeUtils.now_str()
        node = graph.get_node(node_id)
        create_time = node.props.get("create_time")
        node_type = graph.get_node_type(space)
        prop_names = [prop.name for prop in node_type.props]
        has_new_props = False
        for key in props.keys():
            if key not in prop_names:
                has_new_props = True
                node_type.props.append(NodeProp(name=key, type="string", nullable=True))
        if has_new_props:
            graph.alter_node_type(node_type)
        props.update(
            {
                "create_time": create_time or now_time,
                "update_time": now_time,
            }
        )
        node = graph.update_node(node_id, props)
        return self._restore_node_props(node)

    def delete_node_by_name(self, space: str, types: List[str], name: str):
        # TODO: 版本
        graph = self.graph_db.get_graph(space)
        node = graph.nodes.match(types, props=[("name", "=", name)]).first()
        if node is not None:
            graph.delete_node(node.id)

    def delete_node_by_ontology(self, space: str, types: List[str], name: str):
        # TODO: 版本
        graph = self.graph_db.get_graph(space)
        query = (
            f"MATCH (n)-[r]->(m:{':'.join(types)}) WITH n, r, labels(n) AS labels "
            f" WHERE n.{space}.name='{name}'"
            " AND r.name='属于 AND 'concept' IN labels DELETE n"
        )
        graph.client.run(query)

    def get_edges_with_eid(self, space: str, labels: List[str], eid: str):
        graph = self.graph_db.get_graph(space)
        edges = graph.edges.match(
            src_node_types=labels,
            src_node_props=[("eid", "=", eid)],
        ).all()
        return [self._restore_edge_props(edge) for edge in edges]

    def partial_update_edge(self, space: str, edge_id: str, props: dict) -> Edge:
        graph = self.graph_db.get_graph(space)
        now_time = TimeUtils.now_str()
        edge = graph.get_edge(edge_id)
        create_time = edge.props.get("create_time")
        props.update(
            {
                "create_time": create_time or now_time,
                "update_time": now_time,
            }
        )
        edge = graph.update_edge(edge_id, props)
        return self._restore_edge_props(edge)

    def create_edge(self, edge: Edge) -> Edge:
        """创建边（兼容旧接口）"""
        # 从edge对象中提取space信息，如果没有则使用默认值
        space = getattr(edge, 'space', 'default')
        return self.insert_edge(space, edge)

    def save_node(self, node: Node) -> Node:
        """保存节点（兼容旧接口）"""
        # 从node对象中提取space信息，如果没有则使用默认值
        space = getattr(node, 'space', 'default')
        return self.insert_node(space, node)

    def sync_entity_type_by_ontology_name(self, space: str, kg_id: int, ontology_labels: list, old_node_name: str, new_property_value: str) -> None:
        """根据本体名称同步实体的_type属性并移除旧标签"""
        graph = self.graph_db.get_graph(space)
        # Nebula nGQL 查询：查找实体并更新_type属性，移除旧标签
        query = (
            f'MATCH (m)-[r]->(n) WHERE '
            f'r.name == "属于" AND n.name == "{new_property_value}" '
            f'SET m._type = "{new_property_value}"; '
            f'DELETE TAG {old_node_name} FROM m'
        )
        graph.client.run(query)

    def sync_entity_property_rename(self, space: str, ontology_labels: list, node_name: str, old_property_name: str, new_property_name: str) -> None:
        """同步实体属性重命名"""
        graph = self.graph_db.get_graph(space)
        # Nebula nGQL 查询：重命名属性
        query = (
            f'MATCH (m)-[r]->(n) WHERE '
            f'r.name == "属于" AND n.name == "{node_name}" '
            f'SET m.{new_property_name} = m.{old_property_name}; '
            f'ALTER TAG {space} DROP ({old_property_name})'
        )
        graph.client.run(query)

    def sync_entity_property_remove(self, space: str, ontology_labels: list, node_name: str, property_name: str) -> None:
        """同步删除实体属性"""
        graph = self.graph_db.get_graph(space)
        # Nebula nGQL 查询：删除属性
        query = (
            f'MATCH (m)-[r]->(n) WHERE '
            f'r.name == "属于" AND n.name == "{node_name}" '
            f'ALTER TAG {space} DROP ({property_name})'
        )
        graph.client.run(query)

    def sync_entity_relation_rename(self, space: str, kg_id: int, src_type: str, dst_type: str, old_relation_name: str, new_relation_name: str) -> None:
        """同步实体关系重命名"""
        graph = self.graph_db.get_graph(space)
        # Nebula nGQL 查询：重命名关系
        query = (
            f'MATCH (m)-[r]->(n) WHERE '
            f'm._type == "{src_type}" AND r.name == "{old_relation_name}" AND n._type == "{dst_type}" '
            f'SET r.name = "{new_relation_name}"'
        )
        graph.client.run(query)

    def sync_entity_relation_remove(self, space: str, kg_id: int, src_type: str, dst_type: str, relation_name: str) -> None:
        """同步删除实体关系（设置c=null）"""
        graph = self.graph_db.get_graph(space)
        # Nebula nGQL 查询：设置关系的c属性为null
        query = (
            f'MATCH (m)-[r]->(n) WHERE '
            f'm._type == "{src_type}" AND r.name == "{relation_name}" AND n._type == "{dst_type}" '
            f'SET r.c = NULL'
        )
        graph.client.run(query)
