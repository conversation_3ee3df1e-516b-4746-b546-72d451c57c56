from backend.models.kg import GraphDbs

from .base_graph_mapper import BaseGraphMapper
from .nebula_graph_mapper import NebulaGraphMapper
from .neo4j_graph_mapper import Neo4jGraphMapper

__all__ = [
    "NebulaGraphMapper",
    "Neo4jGraphMapper",
    "BaseGraphMapper",
    "get_graph_mapper",
]


def get_graph_mapper(db: GraphDbs, db_config: dict, enable_version: bool = True):
    if db == GraphDbs.NEBULA:
        return NebulaGraphMapper(db_config, enable_version)
    if db == GraphDbs.NEO4J:
        return Neo4jGraphMapper(db_config, enable_version)
    raise ValueError(f"Unsupported db: {db}")
