from yfflow import Client

from backend.models.kg import Kg, KgVersion, OntologyEditLog, ResourceChangeLog, close_old_database_connections
from backend.models.schemas import SyncOntologyEditParams
from backend.modules.save.pipelines import ReindexParams, ReindexPipeline
from backend.modules.update_kbqa.pipelines import UpdateKbqaParams, UpdateKbqaPipeline
from backend.modules.update_ontology_tree.pipelines import UpdateOntologyTreeParams, UpdateOntologyTreePipeline
from backend.utils import conf


class SyncOntologyEditPipeline:
    count_client = Client(**conf["count_conf"])

    def run(self, params: SyncOntologyEditParams) -> None:
        kg = Kg.objects.filter(id=params.kg_id).first()
        if not kg:
            raise ValueError(f"未找到图谱: {params.kg_id}")
        self.start(kg)
        edit_logs = self.get_edit_logs(kg)
        self.end(kg)

    def get_edit_logs(self, kg: Kg) -> list:
        return OntologyEditLog.objects.filter(kg_id=kg.id, status=1)

    def start(self, kg: Kg) -> None:
        # 设置图谱c版本为同步编辑操作中
        kg_version = kg.versions.filter(number=0).first()
        kg_version.sync_status = 2
        kg_version.save(update_fields=["sync_status"])

    def end(self, kg: Kg) -> None:
        # 设置图谱c版本为同步编辑完成
        kg_version = kg.versions.filter(number=0).first()
        kg_version.sync_status = 1
        kg_version.save(update_fields=["sync_status"])
        self.count_client.post("/kg_count", {"kg_id": kg.id, "version_number": 0})
        self.count_client.post(
            "/update_center_node", {"kg_id": kg.id, "version_number": 0}
        )
        ReindexPipeline.run(ReindexParams(kg_id=kg.id, version="c"))
        UpdateOntologyTreePipeline.run(UpdateOntologyTreeParams(kg_id=kg.id, version_number=0))
        UpdateKbqaPipeline.run(UpdateKbqaParams(kg_id=kg.id, version="c"))
        kg_version.save(update_fields=["sync_status"])
        self.count_client.post("/kg_count", {"kg_id": kg.id, "version_number": 0})
        self.count_client.post(
            "/update_center_node", {"kg_id": kg.id, "version_number": 0}
        )
        ReindexPipeline.run(ReindexParams(kg_id=kg.id, version="c"))
        UpdateOntologyTreePipeline.run(UpdateOntologyTreeParams(kg_id=kg.id, version_number=0))
        UpdateKbqaPipeline.run(UpdateKbqaParams(kg_id=kg.id, version="c"))
