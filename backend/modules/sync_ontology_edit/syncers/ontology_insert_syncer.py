from yunfu.db.graph.models import Edge, Node

from backend.graph import BaseGraphMapper
from backend.mappers.graph_mapper import SearchMapper
from backend.models.kg import Kg, OntologyEditLog, ResourceChangeLog
from backend.utils import IDUtils, conf

from .base import BaseSyncer


class OntologyInsertSyncer(BaseSyncer):
    search_mapper = SearchMapper(conf["ES_CONFIGS"])
    index = conf["ES_CONFIGS"]["save_index"]

    def __init__(self, graph_mapper: BaseGraphMapper):
        self.graph_mapper = graph_mapper

    def sync(self, kg: Kg, log: OntologyEditLog) -> None:
        name = log.dst
        eid = IDUtils.get_yfid(name)
        space, labels = kg.space, ["concept"]
        category_node = self._get_root_node_eid(self.index, space, labels, kg.id, "ont")
        category = category_node.name
        node_ = Node(
            id=eid,
            types=labels,
            props={
                "name": name,
                "_eid": eid,
                "_type": category,
                "_show_name": name,
            },
        )
        node = self.graph_mapper.insert_node(space, node_)
        rid = IDUtils.get_yfid("属于")[:6]
        edge = Edge(
            id=rid,
            src_id=node.id,
            type="属于",
            dst_id=category_node.id,
            props={"name": "属于", "_rid": rid},
        )
        edge = self.graph_mapper.insert_edge(space, edge)
        self.search_mapper.create_entity(
            self.index, name, category, "yfkm", eid, int(kg.id), "ont"
        )
        logs = []
        log_entity = {
            "resource_name": node.name,
            "resource_id": node.props["_eid"],
            "resource_type": "entity",
            "operation": "create",
            "value": "",
            "source_id": "",
        }
        logs.append(log_entity)
        resource_id = f'{node.props["_eid"]}{category_node.props["_eid"]}{rid}'
        log_relation = {
            "resource_name": "属于",
            "resource_id": resource_id,
            "resource_type": "relation",
            "operation": "create",
            "value": "",
            "source_id": "",
        }
        logs.append(log_relation)
        ResourceChangeLog.create_logs(logs)

    def _get_root_node_eid(self, index_name, space, labels, kg_id, node_type) -> Node:
        node = self.graph_mapper.get_node_by_name(space, "c", labels, "事物")
        if not node:
            eid = IDUtils.get_yfid("事物")
            node = Node(
                id=eid,
                types=labels,
                props={
                    "name": "事物",
                    "_show_name": "事物",
                    "_eid": eid,
                    "_type": "概念",
                },
            )
            node = self.graph_mapper.insert_node(space, node)
            self.search_mapper.create_entity(
                index_name, node.name, "概念", "yfkm", eid, kg_id, node_type
            )
        return node
