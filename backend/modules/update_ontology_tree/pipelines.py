from backend.graph import get_graph_mapper
from backend.models.kg import Kg, KgVersion, close_old_database_connections
from backend.models.schemas import UpdateOntologyTreeParams
from backend.utils import NodeColorManager


class UpdateOntologyTreePipeline:

    @close_old_database_connections
    @classmethod
    def run(cls, params: UpdateOntologyTreeParams) -> None:
        kg = Kg.objects.filter(id=params.kg_id).first()
        if not kg:
            raise ValueError(f"未找到图谱: {params.kg_id}")
        graph_mapper = get_graph_mapper(kg.db, kg.db_config)
        space = kg.space
        labels = ['concept']
        version = KgVersion.number2str(params.version_number)
        relations = graph_mapper.get_all_onto_relations_by_labels(space, version, labels, '事物')
        ontology_tree = cls.recursion_children(graph_mapper, space, version, labels, relations, [])
        kg_version = kg.versions.filter(number=params.version_number).first()
        kg_version.ontology_tree = ontology_tree
        kg_version.save(update_fields=['ontology_tree'])

    @classmethod
    def recursion_children(cls, graph_mapper, space, version, labels, child_rel, relation_strings):
        children = []
        for child in child_rel:
            inverse_relation = f'{child.dst_node.name}_{child.src_node.name}'
            if inverse_relation in relation_strings:
                continue
            relation_strings.append(f'{child.src_node.name}_{child.dst_node.name}')
            child_relation = graph_mapper.get_all_onto_relations_by_labels(space, version, labels, child.src_node.name)
            next_children = cls.recursion_children(
                graph_mapper, space, version, labels, child_relation, relation_strings)
            children.append({
                'label': child.src_node.name,
                'eid': child.src_node.props['_eid'],
                'has_children': True if next_children else False,
                'children': next_children
            })
        name2color = NodeColorManager.get_node2color([child['label'] for child in children])
        for child in children:
            child['color'] = name2color[child['label']]
        # 对ontologies进行排序，按label的升序
        children = sorted(children, key=lambda x: x['label'])
        return children
