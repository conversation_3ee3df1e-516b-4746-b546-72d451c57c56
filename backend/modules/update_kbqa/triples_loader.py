import re
from typing import List

from backend.graph import BaseGraphMapper


class EntityTriplesLoader:

    def __init__(self, graph_mapper: BaseGraphMapper, limit: int = 1000):
        self.graph_mapper = graph_mapper
        self.limit = limit

    def load(self, space: str, version: str = "c") -> List[str]:
        entities = []
        skip = 0
        while True:
            entities = self.graph_mapper.export_entities(space, skip, self.limit, version)
            data = []
            filters = {
                "name": True,
                "_create_time": True,
                "_update_time": True,
            }
            for entity in entities:
                triples = [
                    [entity.props["name"], key, entity.props[key]]
                    for key in entity.props
                    if not filters.get(key)
                ]
                data += triples
            total = len(data)
            if total < 1:
                break
            skip += self.limit
            lines = []
            for item in data:
                line = ""
                if item[1] == "_type":
                    item[1] = "type"
                if re.search(r"^-?\d+.?\d+$", str(item[2])):
                    line = "<{0}>\t<{1}>\t{2}\t.\n".format(item[0], item[1], item[2])
                else:
                    line = '<{0}>\t<{1}>\t"{2}"\t.\n'.format(item[0], item[1], item[2])
                lines.append(line)
            entities.extend(lines)
            if total < self.limit:
                break
        return entities


class RelationTriplesLoader:

    def __init__(self, graph_mapper: BaseGraphMapper, limit: int = 1000):
        self.graph_mapper = graph_mapper
        self.limit = limit

    def load(self, space: str, version: str = "c") -> List[str]:
        skip = 0
        relations = []
        while True:
            relations = self.graph_mapper.export_relations(space, skip, self.limit, version)
            data = []
            for relation in relations:
                data.append(
                    [
                        relation.src_node.name,
                        relation.props["name"],
                        relation.dst_node.name,
                    ]
                )
            total = len(data)
            if total < 1:
                break
            skip += self.limit
            lines = []
            for item in data:
                line = "<{0}>\t<{1}>\t<{2}>\t.\n".format(item[0], item[1], item[2])
                lines.append(line)
            relations.extend(lines)
            if total < self.limit:
                break
        return relations


def load_triples(graph_mapper: BaseGraphMapper, space: str, version: str = "c") -> List[str]:
    entities = EntityTriplesLoader(graph_mapper).load(space, version)
    relations = RelationTriplesLoader(graph_mapper).load(space, version)
    return entities, relations
