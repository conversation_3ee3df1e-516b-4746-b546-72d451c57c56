from typing import List, Set

from pydantic import BaseModel


class TriplesParseResult(BaseModel):
    relations: List[List[str]]
    properties: List[List[str]]
    types: Set[str]


class TriplesParser:

    bad_names = (
        "heat", "info", "show_name", "source", "uuid", "shares", "update_time",
        "edits", "alias", "summary_pic", "edited_at", "views", "likes", "create_time",
        "baidu_id", "url", "source_id", "name", "imageSrc", "description", "tags",
        "_show_name", "_eid", "_type",
    )

    @classmethod
    def parse(cls, lines: List[str]):
        relations = []
        properties = []
        types = set()
        for line_ in lines:
            line = line_.split("\t")
            if len(line) != 4:
                continue
            subject, name, object_, punctuation = (line[0], line[1], line[2], line[3])
            punctuation = punctuation.replace("\r", "").replace("\n", "")
            symbol_list = [
                subject[0],
                subject[-1],
                name[0],
                name[-1],
                object_[0],
                object_[-1],
                punctuation,
            ]
            if name[1:-1] in cls.bad_names:
                continue
            if name[1:-1] == "type" and object_[1:-1] == "概念":
                types.add(subject[1:-1])
            elif name[1:-1] == "type":
                types.add(object_[1:-1])
            symbol_str = "".join(symbol_list)
            if symbol_str == "<><><>.":
                triple = [subject[1:-1], name[1:-1], object_[1:-1]]
                relations.append(triple)
            elif symbol_str == '<><>"".' or symbol_str == "<><>''.":
                triple = [subject[1:-1], name[1:-1], object_[1:-1]]
                properties.append(triple)
            elif symbol_str.startswith("<><>") and symbol_str.endswith("."):
                try:
                    float(object_.rstrip("."))
                    triple = [subject[1:-1], name[1:-1], object_]
                    properties.append(triple)
                except Exception:
                    continue
        return relations, properties, types
