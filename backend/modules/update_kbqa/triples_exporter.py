import os

from backend.utils import conf

from .triples_parser import TriplesParseResult


class CsvTriplesExporter:

    def __init__(self, kg_id: int, version: str = "version", export_dir: str = conf["export_path"]):
        self.mkdirs(kg_id, version, export_dir)

    def export(self, parse_result: TriplesParseResult):
        self.export_entities(parse_result.relations, parse_result.types, self.file_path)
        properties_ = self.export_properties(parse_result.properties, self.file_path)
        self.export_relations(parse_result.relations, properties_, self.file_path)

    def export_properties(self, triples, file_path):
        """导出属性词表/属性值词表"""
        properties = set()
        property2values = {}
        for triple in triples:
            triple[1] = triple[1].replace(" ", "")
            properties.add(triple[1])
            if property2values.get(triple[1]):
                values = property2values.get(triple[1])
                property2values[triple[1]] = (
                    [values] if isinstance(values, str) else values
                )
                property2values[triple[1]].append(triple[2])
            else:
                property2values[triple[1]] = triple[2]
        with open(f"{file_path}properties.csv", "w", encoding="utf-8") as file:
            for property_ in properties:
                if property_ in [
                    "的",
                    "是",
                    "什",
                    "么",
                    "是什",
                    "什么",
                    "是什么",
                    "?",
                    "？",
                ]:
                    continue
                file.write(f"{property_},{property_}\n")
        with open(f"{file_path}property_values.csv", "w", encoding="utf-8") as file:
            for property_, values in property2values.items():
                if isinstance(values, list):
                    values = set(values)
                    for value in values:
                        file.write(f"{value},{value},{property_}\n")
                else:
                    file.write(f"{values},{values},{property_}\n")
        return properties

    def export_relations(self, triples, properties, file_path):
        """导出关系词表"""
        with open(f"{file_path}relations.csv", "w", encoding="utf-8") as file:
            relations = set()
            for triple in triples:
                triple[1] = triple[1].replace(" ", "")
                if triple[1] in properties:
                    continue
                relations.add(triple[1])
            for relation in relations:
                file.write(f"{relation},{relation}\n")

    def export_entities(self, triples, types, file_path):
        """导出实体词表"""
        type2entities = {}
        for triple in triples:
            if triple[2] in types:
                if type2entities.get(triple[2]):
                    entities_ = type2entities.get(triple[2])
                    type2entities[triple[2]] = (
                        [entities_] if isinstance(entities_, str) else entities_
                    )
                    type2entities[triple[2]].append(triple[0])
                else:
                    type2entities[triple[2]] = [triple[0]]
        # 本体添加到实体词表
        for type_ in types:
            if type2entities.get("概念"):
                type2entities["概念"].append(type_)
            else:
                type2entities["概念"] = [type_]

        with open(f"{file_path}entities.csv", "w", encoding="utf-8") as file:
            for type_, entities in type2entities.items():
                if isinstance(entities, list):
                    entities = set(entities)
                    for entity in entities:
                        file.write(f"{entity},{entity},{type_}\n")
                else:
                    file.write(f"{entities},{entities},{type_}\n")

    def mkdirs(self, kg_id: int, version: str, export_dir: str):
        self.file_path = f"{export_dir}{kg_id}/"
        if version != "c":
            self.file_path = f"{export_dir}{kg_id}/{version}/"
        os.makedirs(self.file_path, exist_ok=True)


def export_triples(
        parse_result: TriplesParseResult, kg_id: int,
        version: str = "version", export_dir: str = conf["export_path"]):
    exporter = CsvTriplesExporter(kg_id, version, export_dir)
    exporter.export(parse_result)
