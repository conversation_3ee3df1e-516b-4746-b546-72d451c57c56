from backend.graph import get_graph_mapper
from backend.models.kg import Kg, close_old_database_connections
from backend.models.schemas import UpdateKbqaParams
from backend.modules.update_kbqa import TriplesParser, export_triples, load_triples


class UpdateKbqaPipeline:

    @close_old_database_connections
    @staticmethod
    def run(params: UpdateKbqaParams) -> None:
        kg = Kg.objects.filter(id=params.kg_id).first()
        if not kg:
            raise ValueError(f"kg_id错误: {params.kg_id}")
        graph_mapper = get_graph_mapper(kg.db, kg.db_config)
        triples = load_triples(graph_mapper, kg.space, params.version)
        parse_result = TriplesParser.parse(triples)
        export_triples(parse_result, params.kg_id, params.version)
