from django.db import close_old_connections

from backend.models.kg import Kg, KgVersion, close_old_database_connections
from backend.models.schemas import ExportKgParams
from backend.modules.export import ExporterFactory, ExportTypes


class ExportKgPipeline:

    @close_old_database_connections
    @staticmethod
    def run(params: ExportKgParams) -> None:
        kg = Kg.objects.filter(id=params.kg_id).first()
        if not kg:
            raise ValueError(f"未找到图谱: {params.kg_id}")
        kg_version = kg.versions.filter(number=params.version_number).first()
        version = KgVersion.number2str(params.version_number)
        for export_type in ExportTypes:
            file_id = ExporterFactory.get_exporter(export_type).export(params.kg_id, version, params.org_id)
            setattr(kg_version, f'{export_type.value}_file', file_id)
        close_old_connections()  # 如果图谱很大，处理时间长，这里的连接可能会断开，需要重新连接
        kg_version.save(update_fields=['entity_file', 'ontology_file', 'kg_file', 'full_file'])
