import re

from yfflow import Yf<PERSON>ogger
from yunfu.common import ConfigUtils

from backend.graph import BaseGraphMapper

from .media_processor import File, YfMediaProcessor
from .models import ExportTypes

conf = ConfigUtils.load('conf/config.yaml')
logger = YfLogger(__name__)


class TripleGenerator:

    @classmethod
    def generate(
            cls, graph_mapper: BaseGraphMapper, space: str,
            version: str, export_type: ExportTypes, with_media: bool = False):
        """生成三元组数据"""
        triples = []
        entity_triples, medias = cls.export_entity_triples(graph_mapper, space, version, export_type, with_media)
        triples.extend(entity_triples)
        triples.extend(cls.export_relation_triples(graph_mapper, space, version, export_type))
        return triples, medias

    @classmethod
    def export_entity_triples(
            cls, graph_mapper: BaseGraphMapper, space: str, version: str,
            export_type: ExportTypes, with_media: bool = False):
        """生成实体三元组数据"""
        triples, medias = [], []
        skip = 0
        limit = 1000
        while True:
            entities = graph_mapper.export_entities(space, skip, limit, version, export_type)
            data = []
            filters = {
                "name": True,
                "_create_time": True,
                "_update_time": True,
            }
            for entity in entities:
                triples = [
                    [entity.name, key, entity.props[key]]
                    for key in entity.props
                    if not filters.get(key)
                ]
                data += triples
            total = len(data)
            skip += limit
            lines = []
            for item in data:
                line = ''
                if re.search(r'^-?\d+.?\d+$', str(item[2])):
                    line = '<{0}>\t<{1}>\t{2}\t.\n'.format(item[0], item[1], item[2])
                else:
                    if with_media:
                        file = File(url=item[2])
                        if file.url:
                            try:
                                media = YfMediaProcessor.process(file)
                                if media:
                                    medias.append(media)
                                    item[2] = media.file_name
                            except Exception as e:
                                logger.error(f'文件处理失败: {file.url}, {e}')
                    line = '<{0}>\t<{1}>\t"{2}"\t.\n'.format(item[0], item[1], item[2])
                lines.append(line)
            triples.extend(lines)
            if total < limit:
                break
        return triples, medias

    @classmethod
    def export_relation_triples(cls, graph_mapper: BaseGraphMapper, space: str, version: str, export_type: ExportTypes):
        """生成关系三元组数据"""
        skip = 0
        limit = 1000
        triples = []
        while True:
            relations = graph_mapper.export_relations(space, skip, limit, version, export_type)
            data = []
            for relation in relations:
                data.append(
                    [
                        relation.src_node.name,
                        relation.props["name"],
                        relation.dst_node.name,
                    ]
                )
            total = len(data)
            skip += limit
            lines = []
            for item in data:
                line = '<{0}>\t<{1}>\t<{2}>\t.\n'.format(item[0], item[1], item[2])
                lines.append(line)
            triples.extend(lines)
            if total < limit:
                break
        return triples
