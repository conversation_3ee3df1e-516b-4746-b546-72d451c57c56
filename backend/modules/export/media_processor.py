import re
from abc import ABC, abstractmethod
from enum import Enum
from pathlib import Path
from typing import Optional

from pydantic import BaseModel, validator
from yfflow import <PERSON><PERSON><PERSON><PERSON><PERSON>
from yunfu.common import ConfigUtils

from backend.utils import MinioUtils, UrlUtils

logger = YfLogger(__name__)
type2suffix = ConfigUtils.load('conf/type2suffix.yaml')
suffix2type = {item: k for k, v in type2suffix.items() for item in v}


class File(BaseModel):
    url: Optional[str]
    config: dict = {}

    @validator('url')
    def validate_url(cls, v: Optional[str]) -> Optional[str]:   # noqa
        if v and UrlUtils.is_valid(v):
            return v
        return None


class MediaTypes(Enum):
    IMAGE = 'image'
    VIDEO = 'video'
    AUDIO = 'audio'
    OTHER = 'other'

    @classmethod
    def from_suffix(cls, suffix: str) -> 'MediaTypes':
        type_ = suffix2type.get(suffix)
        if type_ and type_ in MediaTypes._value2member_map_:    # type: ignore
            return cls(type_)
        return cls.OTHER


class Media(BaseModel):
    name: str
    suffix: str
    content: bytes

    @property
    def type(self) -> MediaTypes:
        return MediaTypes.from_suffix(self.suffix)

    @property
    def file_name(self) -> str:
        return f'{self.type.value}/{self.name}.{self.suffix}'


class BaseMediaProcessor(ABC):

    @classmethod
    @abstractmethod
    def process(cls, file: File) -> Optional[Media]:
        raise NotImplementedError


class YfMediaProcessor(BaseMediaProcessor):
    pattern = re.compile(r'(yfkm)(/.{2}/.{2}/.{2}/.*)')

    @classmethod
    def process(cls, file: File) -> Optional[Media]:
        logger.info(f'获取多模态文件: {file.url}')
        if not file.url:
            return None
        re_result = cls.pattern.search(file.url)
        logger.info(f'url解析结果: {re_result}')
        if not re_result:
            return None
        bucket_name, object_name = re_result.groups()
        data = MinioUtils.download(bucket_name, object_name)
        path = Path(object_name)
        return Media(name=str(path.with_suffix('')).lstrip('/'), suffix=path.suffix.strip('.'), content=data)
