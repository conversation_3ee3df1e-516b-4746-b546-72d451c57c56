
from datetime import datetime

from elasticsearch import Elasticsearch, helpers
from yfflow import <PERSON><PERSON><PERSON><PERSON><PERSON>

from backend.graph import get_graph_mapper
from backend.models.kg import Kg
from backend.models.schemas import CreateParams, ReindexParams
from backend.utils import GraphUtils, IDUtils, conf

logger = YfLogger(__name__)


class CreatePipeline:
    index = conf["ES_CONFIGS"]["save_index"]
    es = Elasticsearch(conf["ES_CONFIGS"]["host"], timeout=180)

    @classmethod
    def run(cls, params: CreateParams) -> None:
        # 1、声明空列表actions
        actions = []
        # 2、遍历documents，获取data和eid，生成action,并添加到actions中
        for document in params.documents:
            eid = list(document.keys())[0]
            data = document.get(eid)
            es_id = data.get("es_id")
            if not es_id:
                es_id = IDUtils.get_yfid(eid)
            action = {
                "_index": cls.index,
                "_id": es_id,
                "_type": "_doc",
                "_source": data,
            }
            actions.append(action)
        # 3、批量插入
        helpers.bulk(cls.es, actions, refresh=True)


class ReindexPipeline:
    index = conf["ES_CONFIGS"]["save_index"]
    es = Elasticsearch(conf["ES_CONFIGS"]["host"], timeout=180)

    @classmethod
    def run(cls, params: ReindexParams) -> None:
        # 1、根据kg_id删除es中对应的数据
        cls._delete_es_data(params.kg_id)
        # 2、根据kg_id去neo4j中查询数据,并批量插入到es中
        cls._search_and_save(params.kg_id)

    @classmethod
    def _delete_es_data(cls, kg_id: int) -> None:
        try:
            cls.es.delete_by_query(
                index=cls.index,
                body={"query": {"bool": {"must": [{"match": {"kg_id": kg_id}}]}}},
                conflicts="proceed",
            )
        except Exception as e:
            logger.error(f"delete_by_query error: {e}")
            return

    @classmethod
    def _search_and_save(cls, kg_id: int) -> None:
        space = GraphUtils.get_label(kg_id)
        kg = Kg.objects.get(id=abs(kg_id))
        graph_mapper = get_graph_mapper(kg.db, kg.db_config)
        if kg_id < 0:
            space = f"KG{kg_id * -1}_ontology"
        ontology_label = GraphUtils.get_ontology_label(kg_id)
        count = graph_mapper.get_nodes_count(space)
        limit = conf["NODES_LIMIT"]
        for i in range(0, int(count / limit) + 1):
            # 1、根据labels去neo4j中查询指定数量的结点
            nodes = graph_mapper.get_nodes(space, i * limit, limit)
            # 2、遍历结点，获取eid和data，生成action,并添加到actions中
            documents = []
            for node in nodes:
                data = {}
                eid = node.props.get("_eid")
                location = None
                relations = graph_mapper.get_all_relations_by_eid(space, eid)
                try:
                    location = {
                        "type": "point",
                        "coordinates": [
                            float(node.props.get("lng") or node.props.get("经度")),
                            float(node.props.get("lat") or node.props.get("纬度")),
                        ],
                    }
                except (ValueError, TypeError):
                    pass
                for key, value in node.props.items():
                    if key.startswith("name@"):
                        data["_".join(key.split("@"))] = value
                    if key.startswith("_type@"):
                        data["_".join(key.split("@"))[1:]] = value
                data["name"] = node.props.get("name")
                data["show_name"] = node.props.get("_show_name")
                data["alias"] = node.props.get("alias")
                data["avatar"] = node.props.get("_avatar")
                data["source_type"] = 3
                data["type"] = node.props.get("_type")
                data["status"] = 1
                data["created_at"] = datetime.now()
                data["updated_at"] = datetime.now()
                data["version"] = [
                    label
                    for label in node.types
                    if label.startswith("v") or label == "c"
                ]
                data["kg_id"] = kg_id
                data["node_type"] = "ont" if ontology_label in node.types else "entity"
                data["eid"] = eid
                data["es_id"] = node.props.get("es_id")
                data["location"] = location
                data["props"] = list(node.props.keys())
                data["relations"] = [relation.props["name"] for relation in relations]

                documents.append({eid: data})
            # 3、批量插入
            CreatePipeline.run(CreateParams(documents=documents))
