from enum import Enum

import typer
from yfflow import Client

app = typer.Typer()

# 数据配置
NEO4J_DATA = {"kg_id": 4314, "version": "c"}
NEBULA_DATA = {"kg_id": 125261, "version": "c"}

class GraphType(str, Enum):
    neo4j = "neo4j"
    nebula = "nebula"

# 初始化客户端
client = Client("127.0.0.1", 10004, "GRPC")

@app.command()
def reindex(graph_type: GraphType):
    """重建索引"""
    data = NEO4J_DATA if graph_type == GraphType.neo4j else NEBULA_DATA
    print(f"正在为 {graph_type} 重建索引...")
    result = client.post("/reindex", data)
    print(f"重建索引结果: {result}")

@app.command()
def update_kbqa(graph_type: GraphType):
    """更新KBQA"""
    data = NEO4J_DATA if graph_type == GraphType.neo4j else NEBULA_DATA
    print(f"正在为 {graph_type} 更新KBQA...")
    result = client.post("/update_kbqa", data)
    print(f"更新KBQA结果: {result}")

@app.command()
def sync_edit(graph_type: GraphType):
    """同步编辑"""
    data = NEO4J_DATA if graph_type == GraphType.neo4j else NEBULA_DATA
    print(f"正在为 {graph_type} 同步编辑...")
    result = client.post("/sync_edit", data)
    print(f"同步编辑结果: {result}")

if __name__ == "__main__":
    app()
