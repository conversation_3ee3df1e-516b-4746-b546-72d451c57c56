class TestSaveGraphMapper:

    def test_get_all_onto_relations_by_labels(self, nebula_mapper, neo4j_mapper, nebula_space
                             , neo4j_space):
        nebula_edges = nebula_mapper.get_all_onto_relations_by_labels(nebula_space, "c", ["concept"], "事物")
        neo4j_edges = neo4j_mapper.get_all_onto_relations_by_labels(neo4j_space, "c", ["concept"], "事物")
        assert len(nebula_edges) == len(neo4j_edges)
