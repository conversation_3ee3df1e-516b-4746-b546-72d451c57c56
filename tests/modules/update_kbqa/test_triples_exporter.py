import os
import shutil
import tempfile

import pytest

from backend.modules.update_kbqa.triples_exporter import (
    CsvTriplesExporter,
    export_triples,
)
from backend.modules.update_kbqa.triples_parser import TriplesParseResult


class TestCsvTriplesExporter:

    @pytest.fixture
    def temp_dir(self):
        """创建临时目录用于测试"""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)

    @pytest.fixture
    def sample_parse_result(self):
        """创建测试用的解析结果"""
        return TriplesParseResult(
            relations=[
                ["张三", "工作于", "公司A"],
                ["李四", "居住在", "北京"],
                ["王五", "年龄", "30"],
                ["赵六", "性别", "男"],
                ["张三", "是", "人"],
                ["公司A", "是什么", "公司"],
                ["北京", "？", "城市"]
            ],
            properties=[
                ["张三", "年龄", "25"],
                ["李四", "身高", "175"],
                ["王五", "体重", "70"],
                ["张三", "年龄", "26"]  # 重复属性用于测试去重
            ],
            types={"人", "公司", "城市"}
        )

    def test_init_default_params(self, temp_dir):
        """测试默认参数初始化"""
        exporter = CsvTriplesExporter(kg_id=123, export_dir=temp_dir + "/")
        expected_path = f"{temp_dir}/123/version/"
        assert exporter.file_path == expected_path
        assert os.path.exists(expected_path)

    def test_init_custom_params(self, temp_dir):
        """测试自定义参数初始化"""
        exporter = CsvTriplesExporter(kg_id=456, version="v1.0", export_dir=temp_dir + "/")
        expected_path = f"{temp_dir}/456/v1.0/"
        assert exporter.file_path == expected_path
        assert os.path.exists(expected_path)

    def test_init_version_c(self, temp_dir):
        """测试版本为'c'时的路径"""
        exporter = CsvTriplesExporter(kg_id=789, version="c", export_dir=temp_dir + "/")
        expected_path = f"{temp_dir}/789/"
        assert exporter.file_path == expected_path
        assert os.path.exists(expected_path)

    def test_mkdirs(self, temp_dir):
        """测试目录创建功能"""
        exporter = CsvTriplesExporter.__new__(CsvTriplesExporter)

        # 测试普通版本
        exporter.mkdirs(123, "v1.0", temp_dir + "/")
        assert exporter.file_path == f"{temp_dir}/123/v1.0/"
        assert os.path.exists(exporter.file_path)

        # 测试版本'c'
        exporter.mkdirs(456, "c", temp_dir + "/")
        assert exporter.file_path == f"{temp_dir}/456/"
        assert os.path.exists(exporter.file_path)

    def test_export_properties(self, temp_dir, sample_parse_result):
        """测试属性导出功能"""
        exporter = CsvTriplesExporter(kg_id=123, export_dir=temp_dir + "/")

        properties = exporter.export_properties(sample_parse_result.properties, exporter.file_path)

        # 检查返回的属性集合
        expected_properties = {"年龄", "身高", "体重"}
        assert properties == expected_properties

        # 检查properties.csv文件
        properties_file = f"{exporter.file_path}properties.csv"
        assert os.path.exists(properties_file)

        with open(properties_file, "r", encoding="utf-8") as f:
            content = f.read()
            assert "年龄,年龄\n" in content
            assert "身高,身高\n" in content
            assert "体重,体重\n" in content

        # 检查property_values.csv文件
        property_values_file = f"{exporter.file_path}property_values.csv"
        assert os.path.exists(property_values_file)

        with open(property_values_file, "r", encoding="utf-8") as f:
            content = f.read()
            # 年龄有两个值：25和26
            assert "25,25,年龄\n" in content
            assert "26,26,年龄\n" in content
            assert "175,175,身高\n" in content
            assert "70,70,体重\n" in content

    def test_export_properties_filtered_keywords(self, temp_dir):
        """测试属性导出时过滤关键词"""
        exporter = CsvTriplesExporter(kg_id=123, export_dir=temp_dir + "/")

        # 包含需要过滤的关键词
        properties_with_keywords = [
            ["张三", "的", "值1"],
            ["李四", "是", "值2"],
            ["王五", "什", "值3"],
            ["赵六", "么", "值4"],
            ["钱七", "是什", "值5"],
            ["孙八", "什么", "值6"],
            ["周九", "是什么", "值7"],
            ["吴十", "?", "值8"],
            ["郑一", "？", "值9"],
            ["陈二", "正常属性", "值10"]
        ]

        parse_result = TriplesParseResult(
            relations=[],
            properties=properties_with_keywords,
            types=set()
        )

        exporter.export_properties(parse_result.properties, exporter.file_path)

        # 检查文件内容
        with open(f"{exporter.file_path}properties.csv", "r", encoding="utf-8") as f:
            content = f.read()
            assert content == "正常属性,正常属性\n"

    def test_export_relations(self, temp_dir, sample_parse_result):
        """测试关系导出功能"""
        exporter = CsvTriplesExporter(kg_id=123, export_dir=temp_dir + "/")

        # 先导出属性以获取属性集合
        properties = exporter.export_properties(sample_parse_result.properties, exporter.file_path)

        # 导出关系
        exporter.export_relations(sample_parse_result.relations, properties, exporter.file_path)

        # 检查relations.csv文件
        relations_file = f"{exporter.file_path}relations.csv"
        assert os.path.exists(relations_file)

        with open(relations_file, "r", encoding="utf-8") as f:
            content = f.read()
            # 这些应该是关系（不在properties中）
            assert "工作于,工作于\n" in content
            assert "居住在,居住在\n" in content
            assert "是,是\n" in content
            assert "是什么,是什么\n" in content
            assert "？,？\n" in content
            # 这些是属性，不应该出现在关系文件中
            assert "年龄" not in content

    def test_export_entities(self, temp_dir, sample_parse_result):
        """测试实体导出功能"""
        exporter = CsvTriplesExporter(kg_id=123, export_dir=temp_dir + "/")

        exporter.export_entities(sample_parse_result.relations, sample_parse_result.types, exporter.file_path)

        # 检查entities.csv文件
        entities_file = f"{exporter.file_path}entities.csv"
        assert os.path.exists(entities_file)

        with open(entities_file, "r", encoding="utf-8") as f:
            content = f.read()
            # 检查实体按类型分组
            assert "张三,张三,人\n" in content
            assert "公司A,公司A,公司\n" in content
            assert "北京,北京,城市\n" in content
            # 检查类型本身被添加到"概念"类型中
            assert "人,人,概念\n" in content
            assert "公司,公司,概念\n" in content
            assert "城市,城市,概念\n" in content

    def test_export_entities_single_entity_per_type(self, temp_dir):
        """测试每个类型只有一个实体的情况"""
        exporter = CsvTriplesExporter(kg_id=123, export_dir=temp_dir + "/")

        relations = [["实体1", "是", "类型1"]]
        types = {"类型1"}

        exporter.export_entities(relations, types, exporter.file_path)

        with open(f"{exporter.file_path}entities.csv", "r", encoding="utf-8") as f:
            content = f.read()
            assert "实体1,实体1,类型1\n" in content
            assert "类型1,类型1,概念\n" in content

    def test_export_entities_existing_concept_type(self, temp_dir):
        """测试已存在'概念'类型的情况"""
        exporter = CsvTriplesExporter(kg_id=123, export_dir=temp_dir + "/")

        relations = [
            ["实体1", "是", "类型1"],
            ["概念实体", "是", "概念"]
        ]
        types = {"类型1", "概念"}

        exporter.export_entities(relations, types, exporter.file_path)

        with open(f"{exporter.file_path}entities.csv", "r", encoding="utf-8") as f:
            content = f.read()
            # 应该包含原有的概念实体和新添加的类型
            assert "概念实体,概念实体,概念\n" in content
            assert "类型1,类型1,概念\n" in content
            assert "概念,概念,概念\n" in content

    def test_export_full_workflow(self, temp_dir, sample_parse_result):
        """测试完整的导出流程"""
        exporter = CsvTriplesExporter(kg_id=123, export_dir=temp_dir + "/")

        exporter.export(sample_parse_result)

        # 检查所有文件都被创建
        assert os.path.exists(f"{exporter.file_path}properties.csv")
        assert os.path.exists(f"{exporter.file_path}property_values.csv")
        assert os.path.exists(f"{exporter.file_path}relations.csv")
        assert os.path.exists(f"{exporter.file_path}entities.csv")

        # 简单验证文件不为空
        for filename in ["properties.csv", "property_values.csv", "relations.csv", "entities.csv"]:
            with open(f"{exporter.file_path}{filename}", "r", encoding="utf-8") as f:
                assert len(f.read().strip()) > 0

    def test_export_triples_function(self, temp_dir, sample_parse_result):
        """测试便利函数export_triples"""
        export_triples(sample_parse_result, kg_id=999, export_dir=temp_dir + "/")

        # 检查文件是否在正确位置创建
        base_path = f"{temp_dir}/999/version/"
        assert os.path.exists(f"{base_path}properties.csv")
        assert os.path.exists(f"{base_path}property_values.csv")
        assert os.path.exists(f"{base_path}relations.csv")
        assert os.path.exists(f"{base_path}entities.csv")

    def test_export_triples_function_custom_params(self, temp_dir, sample_parse_result):
        """测试便利函数export_triples的自定义参数"""
        export_triples(sample_parse_result, kg_id=888, version="test_v", export_dir=temp_dir + "/")

        # 检查文件是否在正确位置创建
        base_path = f"{temp_dir}/888/test_v/"
        assert os.path.exists(f"{base_path}properties.csv")
        assert os.path.exists(f"{base_path}property_values.csv")
        assert os.path.exists(f"{base_path}relations.csv")
        assert os.path.exists(f"{base_path}entities.csv")

    def test_space_removal_in_relations_and_properties(self, temp_dir):
        """测试关系和属性中空格的移除"""
        exporter = CsvTriplesExporter(kg_id=123, export_dir=temp_dir + "/")

        # 包含空格的数据
        relations_with_spaces = [["实体1", "关 系 1", "实体2"]]
        properties_with_spaces = [["实体1", "属 性 1", "值1"]]

        parse_result = TriplesParseResult(
            relations=relations_with_spaces,
            properties=properties_with_spaces,
            types=set()
        )

        # 导出属性
        properties = exporter.export_properties(parse_result.properties, exporter.file_path)
        assert "属性1" in properties  # 空格应该被移除

        # 导出关系
        exporter.export_relations(parse_result.relations, properties, exporter.file_path)

        # 检查文件内容
        with open(f"{exporter.file_path}relations.csv", "r", encoding="utf-8") as f:
            content = f.read()
            assert "关系1,关系1\n" in content  # 空格应该被移除

        with open(f"{exporter.file_path}properties.csv", "r", encoding="utf-8") as f:
            content = f.read()
            assert "属性1,属性1\n" in content  # 空格应该被移除

    def test_empty_data(self, temp_dir):
        """测试空数据的处理"""
        exporter = CsvTriplesExporter(kg_id=123, export_dir=temp_dir + "/")

        empty_parse_result = TriplesParseResult(
            relations=[],
            properties=[],
            types=set()
        )

        exporter.export(empty_parse_result)

        # 检查文件是否创建（即使为空）
        assert os.path.exists(f"{exporter.file_path}properties.csv")
        assert os.path.exists(f"{exporter.file_path}property_values.csv")
        assert os.path.exists(f"{exporter.file_path}relations.csv")
        assert os.path.exists(f"{exporter.file_path}entities.csv")

        # 检查文件内容为空或只包含概念类型
        with open(f"{exporter.file_path}properties.csv", "r", encoding="utf-8") as f:
            assert f.read() == ""

        with open(f"{exporter.file_path}property_values.csv", "r", encoding="utf-8") as f:
            assert f.read() == ""

        with open(f"{exporter.file_path}relations.csv", "r", encoding="utf-8") as f:
            assert f.read() == ""

        with open(f"{exporter.file_path}entities.csv", "r", encoding="utf-8") as f:
            assert f.read() == ""

    def test_property_single_value_vs_multiple_values(self, temp_dir):
        """测试属性单值和多值的处理逻辑"""
        exporter = CsvTriplesExporter(kg_id=123, export_dir=temp_dir + "/")

        # 测试单值属性和多值属性
        properties = [
            ["实体1", "单值属性", "值1"],
            ["实体2", "多值属性", "值2"],
            ["实体3", "多值属性", "值3"],
            ["实体4", "多值属性", "值2"]  # 重复值，应该去重
        ]

        parse_result = TriplesParseResult(
            relations=[],
            properties=properties,
            types=set()
        )

        exporter.export_properties(parse_result.properties, exporter.file_path)

        with open(f"{exporter.file_path}property_values.csv", "r", encoding="utf-8") as f:
            content = f.read()
            # 单值属性
            assert "值1,值1,单值属性\n" in content
            # 多值属性（去重后）
            assert "值2,值2,多值属性\n" in content
            assert "值3,值3,多值属性\n" in content
            # 确保没有重复
            assert content.count("值2,值2,多值属性\n") == 1

    def test_entity_single_vs_multiple_per_type(self, temp_dir):
        """测试每个类型单个实体和多个实体的处理"""
        exporter = CsvTriplesExporter(kg_id=123, export_dir=temp_dir + "/")

        relations = [
            ["实体1", "是", "类型1"],  # 类型1只有一个实体
            ["实体2", "是", "类型2"],  # 类型2有多个实体
            ["实体3", "是", "类型2"],
            ["实体2", "是", "类型2"]   # 重复实体，应该去重
        ]
        types = {"类型1", "类型2"}

        exporter.export_entities(relations, types, exporter.file_path)

        with open(f"{exporter.file_path}entities.csv", "r", encoding="utf-8") as f:
            content = f.read()
            # 单个实体的类型
            assert "实体1,实体1,类型1\n" in content
            # 多个实体的类型（去重后）
            assert "实体2,实体2,类型2\n" in content
            assert "实体3,实体3,类型2\n" in content
            # 确保没有重复
            assert content.count("实体2,实体2,类型2\n") == 1
            # 类型本身作为概念
            assert "类型1,类型1,概念\n" in content
            assert "类型2,类型2,概念\n" in content