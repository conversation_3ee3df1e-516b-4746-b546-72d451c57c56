import unittest
from unittest.mock import patch

from backend.modules.update_kbqa.triples_parser import TriplesParser, TriplesParseResult


class TestTriplesParser(unittest.TestCase):
    """TriplesParser 类的单元测试"""

    def setUp(self):
        """测试前的设置"""
        self.parser = TriplesParser()

    def test_parse_valid_relation_triple(self):
        """测试解析有效的关系三元组"""
        # Mock 输入数据 - 关系三元组格式: <subject><relation><object>.
        mock_lines = [
            "<张三>\t<认识>\t<李四>\t."
        ]

        relations, properties, types = TriplesParser.parse(mock_lines)

        self.assertEqual(len(relations), 1)
        self.assertEqual(relations[0], ["张三", "认识", "李四"])
        self.assertEqual(len(properties), 0)
        self.assertEqual(len(types), 0)

    def test_parse_valid_property_triple_with_quotes(self):
        """测试解析带引号的属性三元组"""
        # Mock 输入数据 - 属性三元组格式: <subject><property>"value".
        mock_lines = [
            '<张三>\t<年龄>\t"25"\t.'
        ]

        relations, properties, types = TriplesParser.parse(mock_lines)

        self.assertEqual(len(relations), 0)
        self.assertEqual(len(properties), 1)
        self.assertEqual(properties[0], ["张三", "年龄", "25"])
        self.assertEqual(len(types), 0)

    def test_parse_valid_property_triple_with_single_quotes(self):
        """测试解析带单引号的属性三元组"""
        # Mock 输入数据 - 属性三元组格式: <subject><property>'value'.
        mock_lines = [
            "<张三>\t<姓名>\t'张三'\t."
        ]

        relations, properties, types = TriplesParser.parse(mock_lines)

        self.assertEqual(len(relations), 0)
        self.assertEqual(len(properties), 1)
        self.assertEqual(properties[0], ["张三", "姓名", "张三"])
        self.assertEqual(len(types), 0)

    def test_parse_numeric_property_triple(self):
        """测试解析数值属性三元组"""
        # Mock 输入数据 - 数值属性三元组
        mock_lines = [
            "<张三>\t<身高>\t180.5\t."
        ]

        relations, properties, types = TriplesParser.parse(mock_lines)

        self.assertEqual(len(relations), 0)
        self.assertEqual(len(properties), 1)
        self.assertEqual(properties[0], ["张三", "身高", "180.5"])
        self.assertEqual(len(types), 0)

    def test_parse_type_concept_triple(self):
        """测试解析类型为概念的三元组"""
        # Mock 输入数据 - type为概念的情况
        mock_lines = [
            "<人>\t<type>\t<概念>\t."
        ]

        relations, properties, types = TriplesParser.parse(mock_lines)

        self.assertEqual(len(relations), 1)
        self.assertEqual(len(properties), 0)
        self.assertEqual(len(types), 1)
        self.assertIn("人", types)

    def test_parse_type_non_concept_triple(self):
        """测试解析类型为非概念的三元组"""
        # Mock 输入数据 - type为其他类型的情况
        mock_lines = [
            "<张三>\t<type>\t<人>\t."
        ]

        relations, properties, types = TriplesParser.parse(mock_lines)

        self.assertEqual(len(relations), 1)
        self.assertEqual(len(properties), 0)
        self.assertEqual(len(types), 1)
        self.assertIn("人", types)

    def test_parse_bad_names_filtered(self):
        """测试过滤掉坏名称的三元组"""
        # Mock 输入数据 - 包含坏名称的三元组应该被过滤
        mock_lines = [
            "<张三>\t<name>\t<张三>\t.",  # name 在 bad_names 中
            "<张三>\t<uuid>\t<123>\t.",  # uuid 在 bad_names 中
            "<张三>\t<认识>\t<李四>\t."   # 正常的关系
        ]

        relations, properties, types = TriplesParser.parse(mock_lines)

        self.assertEqual(len(relations), 1)
        self.assertEqual(relations[0], ["张三", "认识", "李四"])
        self.assertEqual(len(properties), 0)
        self.assertEqual(len(types), 0)

    def test_parse_invalid_line_format(self):
        """测试解析格式不正确的行"""
        # Mock 输入数据 - 格式不正确的行应该被跳过
        mock_lines = [
            "invalid_line",  # 不是4个字段
            "<张三>\t<认识>",  # 只有2个字段
            "<张三>\t<认识>\t<李四>",  # 只有3个字段
            "<张三>\t<认识>\t<李四>\t."  # 正确格式
        ]

        relations, _properties, _types = TriplesParser.parse(mock_lines)

        self.assertEqual(len(relations), 1)
        self.assertEqual(relations[0], ["张三", "认识", "李四"])

    def test_parse_with_carriage_return_and_newline(self):
        """测试处理包含回车换行符的标点符号"""
        # Mock 输入数据 - 包含\r\n的标点符号
        mock_lines = [
            "<张三>\t<认识>\t<李四>\t.\r\n"
        ]

        relations, _properties, _types = TriplesParser.parse(mock_lines)

        self.assertEqual(len(relations), 1)
        self.assertEqual(relations[0], ["张三", "认识", "李四"])

    def test_parse_invalid_numeric_value(self):
        """测试解析无效数值的情况"""
        # Mock 输入数据 - 无效的数值格式
        mock_lines = [
            "<张三>\t<身高>\tabc\t."  # 无法转换为float的值
        ]

        relations, properties, types = TriplesParser.parse(mock_lines)

        # 应该被跳过，不会被添加到任何结果中
        self.assertEqual(len(relations), 0)
        self.assertEqual(len(properties), 0)
        self.assertEqual(len(types), 0)

    def test_parse_mixed_data(self):
        """测试解析混合类型的数据"""
        # Mock 输入数据 - 包含各种类型的三元组
        mock_lines = [
            "<张三>\t<认识>\t<李四>\t.",      # 关系
            '<张三>\t<年龄>\t"25"\t.',       # 属性（引号）
            "<张三>\t<身高>\t180.5\t.",      # 属性（数值）
            "<人>\t<type>\t<概念>\t.",       # 类型（概念）
            "<张三>\t<type>\t<人>\t.",       # 类型（非概念）
            "<张三>\t<name>\t<张三>\t.",     # 坏名称（应被过滤）
            "invalid_line",                  # 无效行（应被跳过）
        ]

        relations, properties, types = TriplesParser.parse(mock_lines)

        # 验证关系
        self.assertEqual(len(relations), 3)
        self.assertEqual(relations[0], ["张三", "认识", "李四"])

        # 验证属性
        self.assertEqual(len(properties), 2)
        self.assertIn(["张三", "年龄", "25"], properties)
        self.assertIn(["张三", "身高", "180.5"], properties)

        # 验证类型
        self.assertEqual(len(types), 1)
        self.assertIn("人", types)

    def test_parse_empty_input(self):
        """测试解析空输入"""
        mock_lines = []

        relations, properties, types = TriplesParser.parse(mock_lines)

        self.assertEqual(len(relations), 0)
        self.assertEqual(len(properties), 0)
        self.assertEqual(len(types), 0)

    def test_bad_names_constant(self):
        """测试坏名称常量的存在"""
        expected_bad_names = (
            "heat", "info", "show_name", "source", "uuid", "shares", "update_time",
            "edits", "alias", "summary_pic", "edited_at", "views", "likes", "create_time",
            "baidu_id", "url", "source_id", "name", "imageSrc", "description", "tags",
            "_show_name", "_eid", "_type",
        )

        self.assertEqual(TriplesParser.bad_names, expected_bad_names)

    @patch('backend.modules.update_kbqa.triples_parser.TriplesParser.parse')
    def test_parse_method_called_with_correct_arguments(self, mock_parse):
        """测试 parse 方法被正确调用"""
        mock_lines = ["<张三>\t<认识>\t<李四>\t."]
        mock_parse.return_value = ([], [], set())

        TriplesParser.parse(mock_lines)

        mock_parse.assert_called_once_with(mock_lines)

    def test_triples_parse_result_model(self):
        """测试 TriplesParseResult 数据模型"""
        # 创建测试数据
        relations = [["张三", "认识", "李四"]]
        properties = [["张三", "年龄", "25"]]
        types = {"人", "概念"}

        # 创建 TriplesParseResult 实例
        result = TriplesParseResult(
            relations=relations,
            properties=properties,
            types=types
        )

        # 验证数据
        self.assertEqual(result.relations, relations)
        self.assertEqual(result.properties, properties)
        self.assertEqual(result.types, types)
