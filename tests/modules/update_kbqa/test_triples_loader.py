from backend.modules.update_kbqa.triples_loader import (
    EntityTriplesLoader,
    RelationTriplesLoader,
)


class TestEntityTriplesLoader:
    """测试 EntityTriplesLoader 类"""

    def test_load_entities_consistency(self, nebula_mapper, neo4j_mapper, nebula_space, neo4j_space, test_version):
        """测试 Nebula 和 Neo4j 的实体三元组加载结果是否一致"""
        # 创建加载器
        nebula_loader = EntityTriplesLoader(nebula_mapper)
        neo4j_loader = EntityTriplesLoader(neo4j_mapper)

        # 加载数据
        nebula_triples = nebula_loader.load(nebula_space, test_version)
        neo4j_triples = neo4j_loader.load(neo4j_space, test_version)

        # 验证结果
        assert len(nebula_triples) == len(neo4j_triples), "Triples count mismatch"


class TestRelationTriplesLoader:
    """测试 RelationTriplesLoader 类"""

    def test_load_relations_consistency(self, nebula_mapper, neo4j_mapper, nebula_space, neo4j_space, test_version):
        """测试 Nebula 和 Neo4j 的关系三元组加载结果是否一致"""
        # 创建加载器
        nebula_loader = RelationTriplesLoader(nebula_mapper)
        neo4j_loader = RelationTriplesLoader(neo4j_mapper)

        # 加载数据
        nebula_triples = nebula_loader.load(nebula_space, test_version)
        neo4j_triples = neo4j_loader.load(neo4j_space, test_version)

        # 验证结果
        assert len(nebula_triples) == len(neo4j_triples), "Triples count mismatch"
