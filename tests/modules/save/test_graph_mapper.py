class TestSaveGraphMapper:

    def test_get_nodes_count(self, nebula_mapper, neo4j_mapper, nebula_space
                             , neo4j_space):
        nebula_count = nebula_mapper.get_nodes_count(nebula_space)
        neo4j_count = neo4j_mapper.get_nodes_count(neo4j_space)
        assert nebula_count == neo4j_count

    def test_get_nodes(self, nebula_mapper, neo4j_mapper, nebula_space
                             , neo4j_space):
        nebula_nodes = nebula_mapper.get_nodes(nebula_space, 0, 1000)
        neo4j_nodes = neo4j_mapper.get_nodes(neo4j_space, 0, 1000)
        assert len(nebula_nodes) == len(neo4j_nodes)


    def test_get_all_relations_by_node(self, nebula_mapper, neo4j_mapper, nebula_space
                             , neo4j_space, nebula_eid, neo4j_eid):
        nebula_edges = nebula_mapper.get_all_relations_by_eid(nebula_space, nebula_eid)
        neo4j_edges = neo4j_mapper.get_all_relations_by_eid(neo4j_space, neo4j_eid)
        assert len(nebula_edges) == len(neo4j_edges)
