from backend.modules.export.models import ExportTypes
from backend.modules.export.triple_generator import TripleGenerator


class TestTripleGenerator:

    def test_generate_method_kg_count_consistency(
        self, nebula_mapper, neo4j_mapper, nebula_space, neo4j_space, test_version
    ):
        # 测试 nebula
        nebula_triples, nebula_medias = TripleGenerator.generate(
            nebula_mapper, nebula_space, test_version, ExportTypes.KG, with_media=False
        )

        # 测试 neo4j
        neo4j_triples, neo4j_medias = TripleGenerator.generate(
            neo4j_mapper, neo4j_space, test_version, ExportTypes.KG, with_media=False
        )

        assert len(nebula_triples) == len(neo4j_triples), \
            f"generate方法KG类型总三元组数量不一致: nebula={len(nebula_triples)}, neo4j={len(neo4j_triples)}"
        assert len(nebula_medias) == len(neo4j_medias), \
            f"generate方法KG类型媒体文件数量不一致: nebula={len(nebula_medias)}, neo4j={len(neo4j_medias)}"

    def test_generate_method_ontology_count_consistency(
        self, nebula_mapper, neo4j_mapper, nebula_space, neo4j_space, test_version
    ):
        # 测试 nebula
        nebula_triples, nebula_medias = TripleGenerator.generate(
            nebula_mapper, nebula_space, test_version, ExportTypes.ONTOLOGY, with_media=False
        )

        # 测试 neo4j
        neo4j_triples, neo4j_medias = TripleGenerator.generate(
            neo4j_mapper, neo4j_space, test_version, ExportTypes.ONTOLOGY, with_media=False
        )

        assert len(nebula_triples) == len(neo4j_triples), \
            f"generate方法ONTOLOGY类型总三元组数量不一致: nebula={len(nebula_triples)}, neo4j={len(neo4j_triples)}"
        assert len(nebula_medias) == len(neo4j_medias), \
            f"generate方法ONTOLOGY类型媒体文件数量不一致: nebula={len(nebula_medias)}, neo4j={len(neo4j_medias)}"

    def test_generate_method_entity_count_consistency(
        self, nebula_mapper, neo4j_mapper, nebula_space, neo4j_space, test_version
    ):
        # 测试 nebula
        nebula_triples, nebula_medias = TripleGenerator.generate(
            nebula_mapper, nebula_space, test_version, ExportTypes.ENTITY, with_media=False
        )

        # 测试 neo4j
        neo4j_triples, neo4j_medias = TripleGenerator.generate(
            neo4j_mapper, neo4j_space, test_version, ExportTypes.ENTITY, with_media=False
        )

        assert len(nebula_triples) == len(neo4j_triples), \
            f"generate方法ENTITY类型总三元组数量不一致: nebula={len(nebula_triples)}, neo4j={len(neo4j_triples)}"
        assert len(nebula_medias) == len(neo4j_medias), \
            f"generate方法ENTITY类型媒体文件数量不一致: nebula={len(nebula_medias)}, neo4j={len(neo4j_medias)}"
