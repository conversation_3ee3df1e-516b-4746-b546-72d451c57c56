kind: bert
default_version: c
NEO4J_CONFIGS:
  db:
    schema: bolt
    host: !env ${GRAPH_HOST|yftool-db-neo4j}
    port: !env ${GRAPH_PORT|7687}
    username: !env ${GRAPH_USERNAME|neo4j}
    password: !env ${GRAPH_PASSWORD|yunfu2017}
    num_threads: 10
nebula:
  host: !env ${NEBULA_HOST|*************}
  port: !env ${NEBULA_PORT|9669}
  username: !env ${NEBULA_USERNAME|root}
  password: !env ${NEBULA_PASSWORD|root}
ES_CONFIGS:
  host: !env ${ES_HOST|http://yftool-db-elasticsearch:9200}
  base: /opt/yunfu/yfproduct/yfkg2/web/knowledge/linking_v38_20210325.json
  event:
    host: !env ${ES_HOST|http://yftool-db-elasticsearch:9200}
    index: event_v1.7
  num_threads: 5
  save_index: kg_nodes
  data_doc_file_index: data_doc_file
  data_doc_user_file_index: data_doc_user_file
minio:
  endpoint: yftool-db-minio:9000
  access_key: xuKIIgW6L7Db55ik
  secret_key: gatXqFGPFRJXDaz4U2lhUAdzyeoDF4N6
  secure: false
IS_ONLINE_ENVIRONMENT: true
ANALYSIS_CONF:
  analysis:
    analyzer:
      ik_searcher:
        type: custom
        tokenizer: ik_smart
        filter:
          - synonym_filter
          - stopwords_filter
    filter:
      synonym_filter:
        type: synonym
        synonyms_path: analysis/synonym.txt
        updateable: True
      stopwords_filter:
        type: stop
        stopwords_path: analysis/stopwords.txt
        updateable: True
stat_conf:
  host: yftool-ops-statsd
  port: 9125
  prefix: ""
count_conf:
  host: yfproduct-yfkm-services-kg-count
  port: 8000
  protocol: GRPC
NODES_LIMIT: 10000
default_label: "default"
version_label: "c"
export_path: /opt/yunfu/yfproduct/yfkm/backend/data/kbqa_words/
export_file_suffix:
  default: triple
  full: kgd
file_parse_url: "http://yfproduct-yfkm-frontend:80/api/data/docs/parse_export_file/parse_file/"
